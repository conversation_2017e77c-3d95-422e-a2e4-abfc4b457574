resolver 127.0.0.11;

map "$cookie_XDEBUG_SESSION$cookie_XDEBUG_PROFILE$cookie_XDEBUG_TRACE" $fastcgi_backend {
    # Nothing for debug
    "" "fastcgi_backend_noxdebug";

    # In all other cases, a debug cookie will be present; use debug container
    default "fastcgi_backend";
}


upstream fastcgi_backend {
    server   unix:/var/run/php-fpm.sock;
}

upstream fastcgi_backend_noxdebug {
    server   unix:/var/run/php-fpm-noxdebug.sock;
}

server {
    listen                      80 default_server;
    listen                      443 default_server http2 ssl;

    client_max_body_size        10M;

    ssl_certificate            /etc/nginx/ssl/cert.crt;
    ssl_certificate_key        /etc/nginx/ssl/cert.key;

    server_name ${DOMAIN};

    set $MAGE_MODE developer;

    set $MAGENTO_ROOT ${MAGENTO_ROOT};
    set $MAGE_RUN_TYPE ${MAGE_RUN_TYPE};
    set $MAGE_RUN_CODE ${MAGE_RUN_CODE};

    include /etc/nginx/conf.d/M2/*.conf;
}